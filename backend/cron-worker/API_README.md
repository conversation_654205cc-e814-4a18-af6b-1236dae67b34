# LinkTrackPro Cron Worker API Documentation

LinkTrackPro Cron Worker 提供多种API接口，用于链接追踪、域名分析和SEO数据收集。

## 目录
- [认证](#认证)
- [批量处理接口](#批量处理接口)
- [单域名查询API](#单域名查询api)
- [响应格式](#响应格式)
- [错误处理](#错误处理)
- [使用示例](#使用示例)
- [部署配置](#部署配置)

---

## 认证

### 开发环境
在开发环境下（`NODE_ENV=development` 或本地域名），所有接口无需认证即可访问。

### 生产环境
生产环境下需要通过以下方式之一进行认证：

1. **HTTP Basic Auth**
   ```bash
   curl -u "username:password" "https://worker.example.com/api/domain-rating?domain=example.com"
   ```

2. **Authorization Header**
   ```bash
   curl -H "Authorization: Bearer your-token" "https://worker.example.com/api/domain-rating?domain=example.com"
   ```

认证配置通过环境变量设置，详见[部署配置](#部署配置)。

---

## 批量处理接口

这些接口用于批量处理所有数据库中的域名，通常由定时任务调用。

### 健康检查
- **端点**: `GET /` 或 `GET /health`
- **描述**: 检查服务状态
- **认证**: 无需认证

```bash
curl "https://worker.example.com/"
# 响应: 🟢 LinkTrackPro Cron Worker (New Schema) - Healthy
```

### 流量数据收集
- **端点**: `GET /trigger/traffic`
- **描述**: 手动触发所有域名的流量数据收集
- **数据源**: SimilarWeb API
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/trigger/traffic"
# 响应: ✅ Traffic data collection completed in 15234ms
```

### 域名评级收集
- **端点**: `GET /trigger/dr`
- **描述**: 手动触发所有域名的Domain Rating收集
- **数据源**: Ahrefs API
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/trigger/dr"
# 响应: ✅ Domain rating collection completed in 25678ms
```

### 外链发现
- **端点**: `GET /trigger/links`
- **描述**: 手动触发所有项目的外链发现
- **数据源**: Google Search Console API
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/trigger/links"
# 响应: ✅ External links collection completed in 18934ms
```

### 索引状态检查
- **端点**: `GET /trigger/indexing`
- **描述**: 手动触发所有域名的索引状态检查
- **数据源**: Google Search API
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/trigger/indexing"
# 响应: ✅ Indexing status check completed in 12456ms
```

### 历史数据清理
- **端点**: `GET /trigger/cleanup`
- **描述**: 清理90天前的历史数据
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/trigger/cleanup"
# 响应: ✅ Historical data cleanup completed in 3456ms
```

### 全量处理
- **端点**: `GET /trigger/all`
- **描述**: 依次执行所有处理器
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/trigger/all"
# 响应: ✅ All processing completed successfully in 67890ms
```

---

## 单域名查询API

这些API接口接受单个域名参数，返回JSON格式的结构化数据。所有API都支持**智能缓存**和**多源数据获取**。

### 域名评级查询
- **端点**: `GET /api/domain-rating`
- **参数**: `domain` (必需) - 要查询的域名
- **数据源**: 
  1. 数据库缓存 (7天内数据)
  2. SimilarWeb API (流量估算DR)
  3. Ahrefs API (官方DR)
  4. RapidAPI (备用DR服务)
- **缓存**: 7天内的数据直接从数据库返回
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/api/domain-rating?domain=example.com"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "domain": "example.com",
    "dr_score": 75,
    "timestamp": "2024-01-01T12:00:00.000Z",
    "source": "ahrefs",
    "cached": false,
    "error": null
  },
  "duration": "2345ms",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 外链数据查询
- **端点**: `GET /api/external-links`
- **参数**: `domain` (必需) - 要查询的域名
- **数据源**: 
  1. 数据库缓存 (24小时内数据)
  2. SimilarWeb API (首选)
  3. Google Custom Search API
  4. RapidAPI (备用外链服务)
- **缓存**: 24小时内的数据直接从数据库返回
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/api/external-links?domain=example.com"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "domain": "example.com",
    "discovered_links": [
      {
        "title": "Link to Example",
        "link": "https://referrer.com/link-to-example",
        "snippet": "This is a great example site..."
      }
    ],
    "total_links": 1250,
    "timestamp": "2024-01-01T12:00:00.000Z",
    "error": null
  },
  "duration": "3456ms",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 流量数据查询
- **端点**: `GET /api/traffic`
- **参数**: `domain` (必需) - 要查询的域名
- **数据源**: 
  1. SimilarWeb API (首选)
  2. RapidAPI (备用流量服务)
- **缓存**: 实时查询，结果存储到数据库
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/api/traffic?domain=example.com"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "domain": "example.com",
    "traffic": 125000,
    "timestamp": "2024-01-01T12:00:00.000Z",
    "error": null
  },
  "duration": "1234ms",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 索引状态查询
- **端点**: `GET /api/indexing`
- **参数**: `domain` (必需) - 要查询的域名
- **数据源**: 
  1. 数据库缓存 (检查discovered_links表)
  2. Google Search API (`link:target_url site:website.com`)
  3. Google Custom Search API (100次/天限制)
  4. **Exa API** (Google API失败时的智能备用)
- **缓存**: 已索引的URL直接从数据库返回
- **认证**: 生产环境需要

```bash
curl "https://worker.example.com/api/indexing?domain=example.com"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "domain": "example.com",
    "is_indexed": true,
    "indexed_pages": 85,
    "total_pages": 100,
    "indexing_issues": ["Some pages have crawl errors"],
    "source": "google_api",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "error": null
  },
  "duration": "2789ms",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```


---

## 智能缓存和多源数据

### 缓存机制
为了提高响应速度和降低API成本，系统实现了智能缓存机制：

- **域名评级 (DR)**: 7天内的数据直接从数据库返回
- **外链数据**: 24小时内的数据直接从数据库返回  
- **索引状态**: 已发现的链接直接从discovered_links表返回
- **流量数据**: 实时查询但结果会存储到数据库

### 多源数据获取
当缓存不可用时，系统会按优先级尝试多个数据源：

#### 域名评级 (DR) 数据源优先级:
1. **数据库缓存** - 7天内数据
2. **SimilarWeb API** - 基于流量估算DR
3. **Ahrefs API** - 官方权威DR数据
4. **RapidAPI** - 备用DR服务
5. **模拟数据** - 所有源都失败时

#### 外链数据源优先级:
1. **数据库缓存** - 24小时内数据
2. **SimilarWeb API** - 首选外链数据源
3. **Google Custom Search** - 使用`link:domain`查询
4. **RapidAPI** - 备用外链服务
5. **模拟数据** - 所有源都失败时

#### 流量数据源优先级:
1. **SimilarWeb API** - 首选流量数据源
2. **RapidAPI** - 备用流量服务
3. **模拟数据** - 所有源都失败时

#### 索引状态检查优先级:
1. **数据库缓存** - 检查discovered_links表
2. **Google Search** - `link:target_url site:website.com`
3. **Google Custom Search API** - 有每日100次限制
4. **Exa API** - 智能语义搜索，支持复杂查询
5. **默认未索引** - 所有检查都失败时

### 响应标识
API响应中包含以下字段来标识数据源：
- `source`: 数据来源 (`"cached"`, `"similarweb"`, `"ahrefs"`, `"google"`, `"exa_api"`, `"rapidapi"`, `"mock"`)
- `cached`: 布尔值，表示是否来自缓存

---

## 响应格式

### 成功响应
所有API接口都返回统一的JSON格式：

```json
{
  "success": true,
  "data": {
    // 具体数据内容
  },
  "duration": "1234ms",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 错误响应
当发生错误时，`success` 字段为 `false`，`data.error` 包含错误信息：

```json
{
  "success": false,
  "data": {
    "domain": "example.com",
    "error": "API rate limit exceeded",
    // 其他字段设为默认值
  },
  "duration": "567ms",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### HTTP状态码
- `200`: 成功
- `400`: 缺少必需参数
- `401`: 认证失败
- `404`: 端点不存在
- `500`: 服务器内部错误

---

## 错误处理

### 参数验证错误
```bash
curl "https://worker.example.com/api/domain-rating"
# 响应: ❌ Missing required parameter: domain (HTTP 400)
```

### 认证错误
```bash
curl "https://worker.example.com/api/domain-rating?domain=example.com"
# 响应: 🔒 Unauthorized (HTTP 401)
```

### API限制错误
当第三方API达到限制时，接口仍然返回结果，但会在 `error` 字段中标明：

```json
{
  "success": false,
  "data": {
    "domain": "example.com",
    "dr_score": 0,
    "error": "Ahrefs API error: 429 Too Many Requests",
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

---

## 使用示例

### JavaScript/Node.js
```javascript
// 查询域名评级
async function getDomainRating(domain, apiKey) {
  const response = await fetch(`https://worker.example.com/api/domain-rating?domain=${domain}`, {
    headers: {
      'Authorization': `Bearer ${apiKey}`
    }
  });
  
  const result = await response.json();
  
  if (result.success) {
    console.log(`${domain} DR Score: ${result.data.dr_score}`);
  } else {
    console.error(`Error: ${result.data.error}`);
  }
  
  return result;
}

// 使用示例
getDomainRating('example.com', 'your-api-key');
```

### Python
```python
import requests

def get_traffic_data(domain, api_key):
    url = f"https://worker.example.com/api/traffic"
    params = {'domain': domain}
    headers = {'Authorization': f'Bearer {api_key}'}
    
    response = requests.get(url, params=params, headers=headers)
    result = response.json()
    
    if result['success']:
        traffic = result['data']['traffic']
        print(f"{domain} monthly traffic: {traffic:,}")
    else:
        print(f"Error: {result['data']['error']}")
    
    return result

# 使用示例
get_traffic_data('example.com', 'your-api-key')
```

### curl 批量查询
```bash
#!/bin/bash

domains=("example.com" "test.com" "demo.org")
base_url="https://worker.example.com/api"
api_key="your-api-key"

for domain in "${domains[@]}"; do
  echo "Processing $domain..."
  
  # 获取DR
  curl -s -H "Authorization: Bearer $api_key" \
    "$base_url/domain-rating?domain=$domain" | jq '.data.dr_score'
  
  # 获取流量
  curl -s -H "Authorization: Bearer $api_key" \
    "$base_url/traffic?domain=$domain" | jq '.data.traffic'
  
  echo "---"
done
```

---

## 部署配置

### 环境变量

```bash
# 数据库配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SCHEMA=public

# API Keys
AHREFS_API_KEY=your-ahrefs-api-key
GOOGLE_API_KEY=your-google-api-key
GOOGLE_SEARCH_ENGINE_ID=your-google-search-engine-id
SIMILARWEB_API_KEY=your-similarweb-api-key
RAPIDAPI_KEY=your-rapidapi-key
EXA_API_KEY=your-exa-api-key

# 认证配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
# 或者使用Bearer Token
API_TOKEN=your-bearer-token

# 环境标识
ENVIRONMENT=production
NODE_ENV=production

# 通知配置
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook
```

### Cloudflare Workers 部署

1. **安装依赖**
   ```bash
   cd backend/cron-worker
   npm install
   ```

2. **配置 wrangler.toml**
   ```toml
   name = "linktrackpro-cron-worker"
   main = "src/index.js"
   compatibility_date = "2024-01-01"
   
   [triggers]
   crons = ["0 0 * * *", "0 0 * * 0"]  # 每日和每周日
   
   [vars]
   ENVIRONMENT = "production"
   ```

3. **设置环境变量**
   ```bash
   # 设置敏感环境变量
   wrangler secret put SUPABASE_KEY
   wrangler secret put AHREFS_API_KEY
   wrangler secret put GOOGLE_API_KEY
   wrangler secret put SIMILARWEB_API_KEY
   wrangler secret put RAPIDAPI_KEY
   wrangler secret put EXA_API_KEY
   wrangler secret put ADMIN_PASSWORD
   wrangler secret put API_TOKEN
   wrangler secret put FEISHU_WEBHOOK_URL
   
   # 设置公开环境变量
   wrangler secret put SUPABASE_URL --text
   wrangler secret put GOOGLE_SEARCH_ENGINE_ID --text
   ```

4. **部署**
   ```bash
   npm run deploy
   ```

### 定时任务配置

Worker 自动配置以下定时任务：

- **每日任务** (`0 0 * * *`): 流量数据收集
- **每周任务** (`0 0 * * 0`): 域名评级、外链发现、索引状态检查、历史数据清理

### 监控和日志

所有操作都会发送报告到飞书群：
- 成功完成的任务统计
- 失败的域名和错误信息
- 处理时间和API调用次数

---

## API 限制和注意事项

### 速率限制
- **Ahrefs API**: 每分钟最多20次请求
- **Google APIs**: 每日10,000次请求
- **SimilarWeb API**: 根据套餐不同

### 数据更新频率
- **流量数据**: 每日更新
- **域名评级**: 每周更新
- **外链数据**: 每周更新
- **索引状态**: 每周更新

### 最佳实践
1. 避免频繁调用相同域名的API
2. 在生产环境中总是使用认证
3. 监控API配额使用情况
4. 合理设置请求超时时间
5. 实现适当的错误重试机制

---

## 问题排查

### 常见问题

**Q: API返回认证错误**
A: 检查环境变量设置，确保在生产环境中配置了正确的认证信息。

**Q: 第三方API调用失败**
A: 检查API密钥是否有效，是否达到了API限制。测试模式下会返回模拟数据。

**Q: 数据库连接失败**
A: 检查Supabase配置，确保URL和密钥正确。

**Q: 定时任务不执行**
A: 检查Cloudflare Workers的cron配置和部署状态。

### 调试模式

在开发环境中启用详细日志：
```bash
export NODE_ENV=development
export DEBUG=true
npm run dev
```

### 健康检查

定期检查服务状态：
```bash
curl "https://worker.example.com/health"
```

---

## 版本更新

### v2.1.0 (当前版本)
- **智能缓存系统**: 支持数据库缓存减少API调用
- **多源数据获取**: 多个API服务商的备用方案
- **性能优化**: DR缓存7天，外链缓存24小时
- **成本控制**: 优先使用缓存数据，减少付费API调用
- **可靠性提升**: 多重备用方案确保服务可用性

### v2.0.0
- 新增单域名查询API
- 支持JSON格式响应
- 改进错误处理
- 添加CORS支持
- 统一响应格式

### v1.0.0
- 基础定时任务功能
- 批量数据处理
- 飞书通知集成

---

## 联系支持

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- 邮件支持
- 飞书群组

---

*最后更新: 2024-01-01*