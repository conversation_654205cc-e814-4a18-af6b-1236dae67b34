/**
 * Domain Rating service - 域名评级服务
 * 专注于业务逻辑，API选择交给智能quota-manager处理
 */

import { extractDomain } from '../lib/utils.js';
import { getCachedDomainRating } from '../db.js';
import { quotaManager } from '../lib/quota-manager.js';

/**
 * Initialize domain rating service with KV namespace for quota management
 */
export function initDomainRatingService(kvNamespace) {
  if (kvNamespace) {
    quotaManager.init(kvNamespace);
  }
}

/**
 * 获取域名评级 - 使用智能API选择
 * quota-manager根据配额、优先级和优化模式自动选择最佳API
 */
export async function fetchDomainRating(domain, optimizationMode = 'cost_first') {
  console.log(`🏆 Fetching Domain Rating for domain: ${domain} (mode: ${optimizationMode})`);
  
  // Step 1: 检查数据库缓存 (7天)
  const cachedData = await getCachedDomainRating(domain);
  if (cachedData) {
    console.log(`✅ Using cached DR data for ${domain}: ${cachedData.dr_score}`);
    return {
      ...cachedData,
      cached: true
    };
  }
  
  console.log(`🤖 No cache found, using intelligent API selection for ${domain}`);
  
  try {
    // 使用智能API选择系统
    const result = await quotaManager.executeWithIntelligentSelection(
      'domain_rating',
      [domain],
      optimizationMode
    );

    if (result.success) {
      console.log(`✅ Domain rating retrieved via ${result.api_selection.selected_api}`);
      
      // 标准化返回格式，适配不同API的数据结构
      const ratingData = normalizeDomainRatingData(result.data, domain, result.api_selection.selected_api);
      
      return {
        ...ratingData,
        timestamp: result.timestamp,
        source: result.api_selection.selected_api,
        quota_used: result.api_info?.quota_used || true,
        api_selection: result.api_selection
      };
    } else {
      console.log(`⚠️ All domain rating APIs failed, using fallback strategy: ${result.fallback_strategy}`);
      return handleDomainRatingFallback(domain, result);
    }
  } catch (error) {
    console.error(`❌ Error in intelligent domain rating fetch for ${domain}:`, error.message);
    return handleDomainRatingFallback(domain, {
      fallback_strategy: 'default_rating',
      error: error.message
    });
  }
}

/**
 * 标准化不同API返回的域名评级数据格式
 */
function normalizeDomainRatingData(apiData, domain, apiSource) {
  const baseData = {
    domain,
    dr_score: 0
  };

  // 适配Ahrefs官方API格式
  if (apiSource === 'ahrefs_official' && apiData.dr_score !== undefined) {
    return {
      ...baseData,
      dr_score: parseInt(apiData.dr_score) || 0,
      url_rating: parseInt(apiData.url_rating) || 0,
      backlinks: parseInt(apiData.backlinks) || 0,
      referring_domains: parseInt(apiData.referring_domains) || 0
    };
  }
  
  // 适配SimilarWeb Overview格式
  if (apiSource === 'similarweb_overview' && (apiData.global_rank || apiData.estimated_monthly_visits)) {
    const estimatedDR = estimateDRFromOverview(apiData);
    return {
      ...baseData,
      dr_score: estimatedDR,
      global_rank: apiData.global_rank,
      country_rank: apiData.country_rank,
      estimated_visits: apiData.estimated_monthly_visits
    };
  }
  
  // 适配SEO分析格式
  if (apiSource === 'seo_analysis' && (apiData.internal_links || apiData.external_links)) {
    const estimatedDR = estimateDRFromSEO(apiData);
    return {
      ...baseData,
      dr_score: estimatedDR,
      internal_links: apiData.internal_links,
      external_links: apiData.external_links,
      word_count: apiData.word_count
    };
  }
  
  // 适配DR Checker格式
  if (apiSource === 'ahrefs_dr_checker' && apiData.dr_score !== undefined) {
    return {
      ...baseData,
      dr_score: parseInt(apiData.dr_score) || 0,
      ahrefs_rank: apiData.ahrefs_rank
    };
  }
  
  // 直接DR分数格式
  if (apiData.dr_score !== undefined) {
    return {
      ...baseData,
      dr_score: parseInt(apiData.dr_score) || 0
    };
  }
  
  // 默认格式
  return baseData;
}

/**
 * 处理域名评级获取失败的回退策略
 */
function handleDomainRatingFallback(domain, failureInfo) {
  console.log(`🔄 Applying domain rating fallback strategy: ${failureInfo.fallback_strategy}`);
  
  const baseResult = {
    domain,
    timestamp: new Date().toISOString(),
    quota_used: false,
    fallback_reason: failureInfo.reason || 'api_unavailable'
  };

  switch (failureInfo.fallback_strategy) {
    case 'default_rating':
      // 根据域名特征生成合理的DR估值
      const estimatedDR = estimateDRFromDomain(domain);
      return {
        ...baseResult,
        dr_score: estimatedDR,
        source: 'fallback_estimated',
        estimation_method: 'domain_analysis'
      };
      
    default:
      return {
        ...baseResult,
        dr_score: 0,
        source: 'fallback_zero',
        error: failureInfo.error
      };
  }
}

/**
 * 根据域名特征估算DR分数（回退策略）
 */
function estimateDRFromDomain(domain) {
  let score = 5; // 基础分数
  
  // 域名长度分数（较短的域名通常更有价值）
  if (domain.length <= 8) score += 15;
  else if (domain.length <= 12) score += 10;
  else if (domain.length <= 16) score += 5;
  
  // 顶级域名分数
  if (domain.endsWith('.com')) score += 10;
  else if (domain.endsWith('.org')) score += 8;
  else if (domain.endsWith('.net')) score += 6;
  
  // 常见关键词加分
  const keywords = ['tech', 'blog', 'news', 'shop', 'store', 'app', 'web', 'dev'];
  for (const keyword of keywords) {
    if (domain.includes(keyword)) {
      score += 5;
      break;
    }
  }
  
  // 限制在合理范围内
  return Math.min(Math.max(score, 1), 25);
}

/**
 * 批量获取多个域名的评级（优化配额使用）
 */
export async function fetchBatchDomainRatings(domains, optimizationMode = 'quota_preserve') {
  console.log(`🏆 Batch fetching domain ratings for ${domains.length} domains`);
  
  const results = [];
  
  for (const domain of domains) {
    try {
      const result = await fetchDomainRating(domain, optimizationMode);
      results.push(result);
      
      // 批量处理时添加小延迟避免频率限制
      if (domains.length > 5) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error(`Error fetching domain rating for ${domain}:`, error.message);
      results.push(handleDomainRatingFallback(domain, {
        fallback_strategy: 'default_rating',
        error: error.message
      }));
    }
  }
  
  return results;
}

/**
 * 基于SimilarWeb概览数据估算DR分数
 */
function estimateDRFromOverview(overviewData) {
  let score = 0;
  
  // 基于全球排名计算分数 (排名越高分数越高)
  if (overviewData.global_rank) {
    if (overviewData.global_rank <= 1000) score += 70;
    else if (overviewData.global_rank <= 10000) score += 50;
    else if (overviewData.global_rank <= 100000) score += 30;
    else if (overviewData.global_rank <= 1000000) score += 15;
    else score += 5;
  }
  
  // 基于预估月访问量计算分数
  if (overviewData.estimated_monthly_visits) {
    const visits = overviewData.estimated_monthly_visits;
    if (visits >= 10000000) score += 25; // 1000万+
    else if (visits >= 1000000) score += 20; // 100万+
    else if (visits >= 100000) score += 15; // 10万+
    else if (visits >= 10000) score += 10; // 1万+
    else if (visits >= 1000) score += 5; // 1千+
  }
  
  // 限制分数在0-100之间
  return Math.min(Math.max(score, 0), 100);
}

/**
 * 基于SEO分析数据估算DR分数
 */
function estimateDRFromSEO(seoData) {
  let score = 0;
  
  // 基于外部链接数量
  if (seoData.external_links) {
    if (seoData.external_links >= 1000) score += 40;
    else if (seoData.external_links >= 500) score += 30;
    else if (seoData.external_links >= 100) score += 20;
    else if (seoData.external_links >= 50) score += 15;
    else if (seoData.external_links >= 10) score += 10;
    else score += 5;
  }
  
  // 基于内部链接数量（内容结构指标）
  if (seoData.internal_links) {
    if (seoData.internal_links >= 100) score += 20;
    else if (seoData.internal_links >= 50) score += 15;
    else if (seoData.internal_links >= 20) score += 10;
    else if (seoData.internal_links >= 10) score += 5;
  }
  
  // 基于页面字数（内容质量指标）
  if (seoData.word_count) {
    if (seoData.word_count >= 2000) score += 15;
    else if (seoData.word_count >= 1000) score += 10;
    else if (seoData.word_count >= 500) score += 5;
  }
  
  // 基础分数（有基本SEO结构）
  if (seoData.title && seoData.description) score += 10;
  
  // 限制分数在0-100之间
  return Math.min(Math.max(score, 0), 100);
}

/**
 * 为URL获取域名评级
 */
export async function fetchDomainRatingForUrl(url) {
  const domain = extractDomain(url);
  if (!domain) {
    return {
      url,
      dr_score: 0,
      error: 'Invalid URL',
      timestamp: new Date().toISOString()
    };
  }
  
  const result = await fetchDomainRating(domain);
  return { ...result, url };
}




/**
 * 获取域名评级数据收集的API状态分析
 */
export async function getDomainRatingApiAnalytics() {
  try {
    const analytics = await quotaManager.getApiAnalytics('domain_rating');
    return {
      success: true,
      data: analytics,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 智能域名评级优化模式说明：
 * - 'balanced': 平衡模式，综合考虑成本、质量、可靠性
 * - 'cost_first': 成本优先，优先使用免费或低成本API
 * - 'quality_first': 质量优先，优先使用数据质量最高的API
 * - 'quota_preserve': 配额保护，优先使用配额影响最小的API
 */