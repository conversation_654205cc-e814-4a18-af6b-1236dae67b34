/**
 * Universal API Quota Manager & Intelligent API Router
 * Manages all API usage limits and prevents overuse for:
 * - RapidAPI endpoints
 * - Direct API endpoints (Google, Exa, Ahrefs, SimilarWeb)
 * 
 * Provides intelligent API selection based on:
 * - Business type (traffic, indexing, domain-rating, external-links)
 * - Quota availability
 * - API priority and reliability
 * - Cost optimization
 */

import { kvStore } from './kv-store.js';
import { API_CONFIGS } from './api-adapter.js';

export class QuotaManager {
  constructor() {
    this.kvStore = kvStore;
  }

  /**
   * Initialize with Cloudflare KV namespace
   */
  init(kvNamespace) {
    this.kvStore.setNamespace(kvNamespace);
  }

  /**
   * Extract API host from URL
   */
  extractApiHost(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      console.error('Invalid URL:', url);
      return null;
    }
  }

  /**
   * Get quota configuration for an API host
   * References centralized configuration from api-adapter.js
   */
  getQuotaConfig(apiHost) {
    // Search through all API categories to find host
    for (const category of Object.values(API_CONFIGS)) {
      if (category.sources) {
        const source = category.sources.find(s => s.host === apiHost);
        if (source) {
          return {
            monthly_limit: source.monthly_limit,
            daily_limit: source.daily_limit,
            allow_pay: source.allow_pay
          };
        }
      }
    }
    
    // Return default if not found
    return {
      monthly_limit: 100,
      daily_limit: 10,
      allow_pay: true
    };
  }

  /**
   * Check if API call is allowed within quota limits
   */
  async canMakeRequest(apiUrl) {
    const apiHost = this.extractApiHost(apiUrl);
    if (!apiHost) return false;

    const quotaConfig = this.getQuotaConfig(apiHost);
    if (!quotaConfig) return true; // No limits configured

    // Check monthly limit
    if (quotaConfig.monthly_limit) {
      const monthlyUsage = await this.kvStore.getMonthlyUsage(apiHost);
      if (monthlyUsage >= quotaConfig.monthly_limit) {
        if (!quotaConfig.allow_pay) {
          console.warn(`Monthly quota exceeded for ${apiHost}: ${monthlyUsage}/${quotaConfig.monthly_limit}`);
          return false;
        }
        console.info(`Monthly quota exceeded for ${apiHost} but allow_pay is enabled: ${monthlyUsage}/${quotaConfig.monthly_limit}`);
      }
    }

    // Check daily limit
    if (quotaConfig.daily_limit) {
      const dailyUsage = await this.kvStore.getDailyUsage(apiHost);
      if (dailyUsage >= quotaConfig.daily_limit) {
        if (!quotaConfig.allow_pay) {
          console.warn(`Daily quota exceeded for ${apiHost}: ${dailyUsage}/${quotaConfig.daily_limit}`);
          return false;
        }
        console.info(`Daily quota exceeded for ${apiHost} but allow_pay is enabled: ${dailyUsage}/${quotaConfig.daily_limit}`);
      }
    }

    return true;
  }

  /**
   * Record API usage after successful call
   */
  async recordUsage(apiUrl) {
    const apiHost = this.extractApiHost(apiUrl);
    if (!apiHost) return;

    const quotaConfig = this.getQuotaConfig(apiHost);
    if (!quotaConfig) return;

    // Increment counters
    if (quotaConfig.monthly_limit) {
      await this.kvStore.incrementMonthlyUsage(apiHost);
    }
    if (quotaConfig.daily_limit) {
      await this.kvStore.incrementDailyUsage(apiHost);
    }

    console.log(`Recorded usage for ${apiHost}`);
  }

  /**
   * Get current usage statistics for an API
   */
  async getUsageStats(apiUrl) {
    const apiHost = this.extractApiHost(apiUrl);
    if (!apiHost) return null;

    const quotaConfig = this.getQuotaConfig(apiHost);
    if (!quotaConfig) return null;

    const monthlyUsage = await this.kvStore.getMonthlyUsage(apiHost);
    const dailyUsage = await this.kvStore.getDailyUsage(apiHost);

    return {
      apiHost,
      monthly: {
        used: monthlyUsage,
        limit: quotaConfig.monthly_limit,
        remaining: quotaConfig.monthly_limit ? quotaConfig.monthly_limit - monthlyUsage : null
      },
      daily: {
        used: dailyUsage,
        limit: quotaConfig.daily_limit,
        remaining: quotaConfig.daily_limit ? quotaConfig.daily_limit - dailyUsage : null
      },
      allow_pay: quotaConfig.allow_pay
    };
  }

  /**
   * Wrapper for making quota-controlled API requests
   */
  async makeRequest(apiUrl, requestOptions = {}) {
    // Check quota before making request
    const canMake = await this.canMakeRequest(apiUrl);
    if (!canMake) {
      const stats = await this.getUsageStats(apiUrl);
      throw new Error(`Quota exceeded for ${stats.apiHost}. Monthly: ${stats.monthly.used}/${stats.monthly.limit}, Daily: ${stats.daily.used}/${stats.daily.limit}`);
    }

    try {
      // Make the actual request
      const response = await fetch(apiUrl, requestOptions);
      
      // Record usage only if request was successful
      if (response.ok) {
        await this.recordUsage(apiUrl);
      }
      
      return response;
    } catch (error) {
      console.error('API request failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up old quota data
   */
  async cleanup() {
    await this.kvStore.cleanupOldQuotas();
  }

  /**
   * Reset quota for testing purposes
   */
  async resetQuota(apiUrl) {
    const apiHost = this.extractApiHost(apiUrl);
    if (!apiHost) return;

    const monthlyKey = this.kvStore.getMonthlyKey(apiHost);
    const dailyKey = this.kvStore.getDailyKey(apiHost);
    
    await this.kvStore.delete(monthlyKey);
    await this.kvStore.delete(dailyKey);
    
    console.log(`Reset quota for ${apiHost}`);
  }

  /**
   * ===== INTELLIGENT API SELECTION SYSTEM =====
   */

  /**
   * Get API strategy configuration for different business types
   * References centralized configuration from api-adapter.js
   */
  getApiStrategy(businessType) {
    const config = API_CONFIGS[businessType];
    if (!config) return null;
    
    // Transform new format to old format for backward compatibility
    return {
      name: config.name,
      primary_sources: config.sources,
      fallback_strategy: config.fallback_strategy,
      min_reliability_threshold: 0.6 // Default threshold
    };
  }

  /**
   * Intelligent API selection based on business type and quota availability
   * Simplified logic: prioritize free quota available APIs, then allow_pay APIs
   */
  async selectBestApi(businessType, optimizationMode = 'quota_first') {
    const strategy = this.getApiStrategy(businessType);
    if (!strategy) {
      throw new Error(`No API strategy found for business type: ${businessType}`);
    }

    console.log(`🤖 Selecting best API for ${strategy.name} with ${optimizationMode} optimization`);

    // Score each API source based on simplified criteria
    const scoredApis = await Promise.all(
      strategy.primary_sources.map(async (source) => {
        const quotaStats = await this.getUsageStats(`https://${source.host}/test`);
        const canUse = await this.canMakeRequest(`https://${source.host}/test`);
        
        // Simplified scoring: priority + quota availability
        let score = 0;
        
        if (canUse) {
          // Priority score (lower priority number = higher score)
          score = 100 - source.priority;
          
          // Boost score for free APIs with quota available
          if (!source.allow_pay) {
            score += 50; // Prefer free APIs
          }
        }
        
        return {
          ...source,
          score: canUse ? score : 0,
          quota_available: canUse,
          quota_stats: quotaStats
        };
      })
    );

    // Sort by score (highest first) and filter available APIs
    const availableApis = scoredApis
      .filter(api => api.quota_available && api.score > 0)
      .sort((a, b) => b.score - a.score);

    if (availableApis.length === 0) {
      console.warn(`⚠️ No available APIs for ${businessType}`);
      return {
        selected: null,
        fallback_strategy: strategy.fallback_strategy,
        reason: 'quota_exhausted'
      };
    }

    const selectedApi = availableApis[0];
    console.log(`✅ Selected API: ${selectedApi.id} (score: ${selectedApi.score}, priority: ${selectedApi.priority})`);
    
    return {
      selected: selectedApi,
      alternatives: availableApis.slice(1, 3), // Top 2 alternatives
      fallback_strategy: strategy.fallback_strategy,
      reason: 'optimal_selection'
    };
  }

  /**
   * Execute API call with intelligent selection and automatic fallback
   */
  async executeWithIntelligentSelection(businessType, requestParams, optimizationMode = 'quota_first') {
    const selection = await this.selectBestApi(businessType, optimizationMode);
    
    if (!selection.selected) {
      return {
        success: false,
        error: 'No available APIs',
        fallback_strategy: selection.fallback_strategy,
        reason: selection.reason
      };
    }

    // Import api-adapter for actual API calls
    const { apiClient } = await import('./api-adapter.js');
    
    // Prepare method name based on API selection
    const methodMap = {
      'similarweb_direct': 'getSimilarWebDirectTraffic',
      'similarweb_traffic': 'getSimilarWebTrafficDirect',
      'similarweb_rapidapi': 'getSimilarWebTraffic',
      'google_link_site': 'getGoogleIndexingStatus',
      'google_site_search': 'getGoogleIndexingStatus',
      'exa_ai': 'getExaIndexingStatus',
      'google_backlinks': 'getGoogleBacklinks',
      'ahrefs2_rapidapi': 'getAhrefs2Backlinks',
      'ahrefs_rapidapi': 'getAhrefsBacklinks',
      'ahrefs_official': 'getAhrefsDomainRating',
      'ahrefs_api_rapidapi': 'getAhrefsAPIDomainRating',
      'similarweb_overview': 'getSimilarWebOverview',
      'seo_analysis': 'getSEOAnalysis',
      'ahrefs_dr_checker': 'getAhrefsDRChecker',
      'api_layer_whois': 'getWhoisInfoFromAPILayer'
    };

    const methodName = methodMap[selection.selected.id];
    if (!methodName || !apiClient[methodName]) {
      throw new Error(`API method not found: ${methodName}`);
    }

    try {
      // Execute primary API call
      console.log(`🚀 Executing ${methodName} with intelligent selection`);
      
      let result;
      if (selection.selected.id.includes('google_') && selection.selected.search_type) {
        // Special handling for Google APIs with search type
        result = await apiClient[methodName](...requestParams, selection.selected.search_type);
      } else {
        result = await apiClient[methodName](...requestParams);
      }
      
      if (result.success) {
        return {
          ...result,
          api_selection: {
            selected_api: selection.selected.id,
            score: selection.selected.score,
            optimization_mode: optimizationMode
          }
        };
      }
      
      // Check if this is a structured API error that should be passed through
      if (result.errorType === 'api_error') {
        console.log(`⚠️ Primary API ${selection.selected.id} returned structured error: ${result.error}`);
        return {
          ...result,
          api_selection: {
            selected_api: selection.selected.id,
            score: selection.selected.score,
            optimization_mode: optimizationMode
          }
        };
      }
      
      // If primary fails, try alternatives
      console.log(`⚠️ Primary API ${selection.selected.id} failed, trying alternatives...`);
      
      for (const alternative of selection.alternatives) {
        const altMethodName = methodMap[alternative.id];
        if (altMethodName && apiClient[altMethodName]) {
          try {
            console.log(`🔄 Trying alternative: ${alternative.id}`);
            const altResult = await apiClient[altMethodName](...requestParams);
            if (altResult.success) {
              return {
                ...altResult,
                api_selection: {
                  selected_api: alternative.id,
                  score: alternative.score,
                  optimization_mode: optimizationMode,
                  fallback_used: true
                }
              };
            }
          } catch (error) {
            console.log(`⚠️ Alternative ${alternative.id} also failed:`, error.message);
          }
        }
      }
      
      // All APIs failed
      return {
        success: false,
        error: 'All API alternatives failed',
        fallback_strategy: selection.fallback_strategy,
        api_selection: {
          attempted_apis: [selection.selected.id, ...selection.alternatives.map(alt => alt.id)],
          optimization_mode: optimizationMode
        }
      };
      
    } catch (error) {
      console.error(`❌ API execution failed:`, error.message);
      return {
        success: false,
        error: error.message,
        fallback_strategy: selection.fallback_strategy,
        api_selection: {
          selected_api: selection.selected.id,
          optimization_mode: optimizationMode
        }
      };
    }
  }

  /**
   * Get API selection analytics
   */
  async getApiAnalytics(businessType) {
    const strategy = this.getApiStrategy(businessType);
    if (!strategy) return null;

    const analytics = {
      business_type: businessType,
      strategy_name: strategy.name,
      available_apis: strategy.primary_sources.length,
      api_status: []
    };

    for (const source of strategy.primary_sources) {
      const stats = await this.getUsageStats(`https://${source.host}/test`);
      const canUse = await this.canMakeRequest(`https://${source.host}/test`);
      
      analytics.api_status.push({
        id: source.id,
        host: source.host,
        priority: source.priority,
        available: canUse,
        quota_stats: stats
      });
    }

    return analytics;
  }
}

// Export singleton instance
export const quotaManager = new QuotaManager();